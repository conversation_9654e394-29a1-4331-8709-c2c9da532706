# -*- coding: utf-8 -*-
from markitdown import MarkItDown, StreamInfo
from PIL import Image
from openai import OpenAI
import json
import sys
import os
import mimetypes
import subprocess
# client = AzureOpenAI(
#     api_key="69e6ad9453fb4bd2b0f387efd37d940e",  
#     api_version="2023-12-01-preview",
#     azure_endpoint="https://tw-openai-wus3-azure.zxzt123.com"
# )

# 压缩的目标图片大小，300KB
target_size = 500 * 1024

def json_return(code, message, data):
    return_data = {'code': code, 'message': message, 'data': data}
    print(json.dumps(return_data, ensure_ascii=False))

def detect_file_type(filename):
    """检测文件的实际类型，而不仅仅依赖扩展名"""
    try:
        # 读取文件开头的几个字节来判断实际格式
        with open(filename, 'rb') as f:
            header = f.read(1024)

        # 检查是否是CSV文件（UTF-8 BOM + 文本内容）
        if header.startswith(b'\xef\xbb\xbf'):  # UTF-8 BOM
            # 去掉BOM后检查是否包含CSV特征
            content = header[3:].decode('utf-8', errors='ignore')
            if ',' in content or '\t' in content:
                return 'csv'

        # 检查是否是纯文本CSV
        try:
            content = header.decode('utf-8', errors='ignore')
            if ',' in content and '\n' in content:
                return 'csv'
        except:
            pass

        # 检查Excel文件头
        if header.startswith(b'\xd0\xcf\x11\xe0'):  # OLE文件头 (.xls)
            return 'xls'
        elif header.startswith(b'PK\x03\x04'):  # ZIP文件头 (.xlsx)
            return 'xlsx'

        return None
    except Exception:
        return None

def create_corrected_stream_info(filename, detected_type):
    """根据检测到的文件类型创建正确的StreamInfo"""
    type_mapping = {
        'csv': {'mimetype': 'text/csv', 'extension': '.csv'},
        'xls': {'mimetype': 'application/vnd.ms-excel', 'extension': '.xls'},
        'xlsx': {'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'extension': '.xlsx'},
    }

    if detected_type in type_mapping:
        mapping = type_mapping[detected_type]
        return StreamInfo(
            filename=filename,
            mimetype=mapping['mimetype'],
            extension=mapping['extension']
        )
    return None

def detect_file_type_with_system(filename):
    """使用系统file命令检测文件类型"""
    try:
        result = subprocess.run(['file', filename], capture_output=True, text=True)
        output = result.stdout.lower()

        if 'csv' in output or 'text' in output:
            return 'csv'
        elif 'microsoft excel' in output or 'ole' in output:
            return 'xls'
        elif 'zip' in output and filename.endswith('.xlsx'):
            return 'xlsx'

        return None
    except Exception:
        return None

def compress_image(input_path, output_path):
    img = Image.open(input_path)
    current_size = os.path.getsize(input_path)

    # 粗略的估计压缩质量，也可以从常量开始，逐步减小压缩质量，直到文件大小小于目标大小
    image_quality = int(float(target_size / current_size) * 100)
    img.save(output_path, optimize=True, quality=int(float(target_size / current_size) * 100))

    # 如果压缩后文件大小仍然大于目标大小，则继续压缩
    # 压缩质量递减，直到文件大小小于目标大小
    while os.path.getsize(output_path) > target_size:
        img = Image.open(output_path)
        image_quality -= 10
        if image_quality <= 0:
            break
        img.save(output_path, optimize=True, quality=image_quality)
    return image_quality

def main():
    if len(sys.argv) != 2:
        json_return(-1, '错误的传参', {})
        sys.exit(1)
    
    filename = sys.argv[1]  # 第一个参数是文件名
    # print(f"接收到的文件名: {filename}")

    client = OpenAI(
        api_key="a784bd8d-87f0-4f9b-bcb6-e080deb6a868",
        base_url="https://ark.cn-beijing.volces.com/api/v3"
    )

    md = MarkItDown(llm_client=client, llm_model="doubao-1-5-vision-pro-32k-250115")

    # 检测文件实际类型并创建正确的StreamInfo
    detected_type = detect_file_type(filename)
    if not detected_type:
        detected_type = detect_file_type_with_system(filename)

    stream_info = create_corrected_stream_info(filename, detected_type)

    if stream_info:
        result = md.convert(filename, stream_info=stream_info)
    else:
        result = md.convert(filename)

    # with open('document.md', 'w', encoding="utf-8", errors='ignore') as f:
    #     f.write(result.text_content)
    # 成功情况下返回转换后的内容
    json_return(0, 'success', str(result.text_content))
if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        json_return(-1, str(e), {})