# -*- coding: utf-8 -*-
from markitdown import MarkItDown, StreamInfo
from PIL import Image
from openai import OpenAI
import json
import sys
import os
import mimetypes
import subprocess
import tempfile
# client = AzureOpenAI(
#     api_key="69e6ad9453fb4bd2b0f387efd37d940e",  
#     api_version="2023-12-01-preview",
#     azure_endpoint="https://tw-openai-wus3-azure.zxzt123.com"
# )

# 压缩的目标图片大小，300KB
target_size = 500 * 1024

def json_return(code, message, data):
    return_data = {'code': code, 'message': message, 'data': data}
    print(json.dumps(return_data, ensure_ascii=False))

def detect_file_type(filename):
    """检测文件的实际类型，而不仅仅依赖扩展名"""
    try:
        # 读取文件开头的几个字节来判断实际格式
        with open(filename, 'rb') as f:
            header = f.read(1024)

        # 如果文件为空，返回None
        if not header:
            return None

        # 优先检查常见的二进制文件格式（通过魔数/文件头）

        # 图片格式检测
        if header.startswith(b'\x89PNG\r\n\x1a\n'):
            return 'png'
        elif header.startswith(b'\xff\xd8\xff'):
            return 'jpeg'
        elif header.startswith(b'GIF87a') or header.startswith(b'GIF89a'):
            return 'gif'
        elif header.startswith(b'BM'):  # BMP文件头
            return 'bmp'
        elif header.startswith(b'RIFF') and b'WEBP' in header[:12]:
            return 'webp'

        # 文档格式检测
        elif header.startswith(b'%PDF'):
            return 'pdf'
        elif header.startswith(b'\xd0\xcf\x11\xe0'):  # OLE文件头 (.xls, .doc, .ppt等)
            return 'xls'  # 默认为xls，可能需要进一步区分
        elif header.startswith(b'PK\x03\x04'):  # ZIP文件头 (可能是.xlsx, .docx, .pptx等)
            # 根据文件扩展名进一步判断
            ext = filename.lower().split('.')[-1] if '.' in filename else ''
            if ext == 'xlsx':
                return 'xlsx'
            elif ext == 'docx':
                return 'docx'
            elif ext == 'pptx':
                return 'pptx'
            else:
                return 'zip'

        # 音频/视频格式
        elif header.startswith(b'ID3') or header.startswith(b'\xff\xfb'):  # MP3
            return 'mp3'
        elif header.startswith(b'RIFF') and b'WAVE' in header[:12]:
            return 'wav'

        # 只有在确认不是二进制文件的情况下才检查文本格式
        # 先尝试检测是否为纯文本
        is_text = True
        try:
            # 检查前1024字节是否都是可打印的文本字符
            test_content = header.decode('utf-8', errors='strict')
            # 检查是否包含过多的控制字符（除了常见的\n, \r, \t）
            control_chars = sum(1 for c in test_content if ord(c) < 32 and c not in '\n\r\t')
            if control_chars > len(test_content) * 0.1:  # 如果控制字符超过10%，可能不是文本文件
                is_text = False
        except UnicodeDecodeError:
            is_text = False

        # 如果确认是文本文件，再检查具体格式
        if is_text:
            try:
                # 处理UTF-8 BOM
                content = header
                if content.startswith(b'\xef\xbb\xbf'):
                    content = content[3:]

                text_content = content.decode('utf-8', errors='ignore')

                # CSV检测：更严格的条件
                if _is_likely_csv(text_content, filename):
                    return 'csv'

                # 其他文本格式检测
                if filename.lower().endswith('.json') and (text_content.strip().startswith('{') or text_content.strip().startswith('[')):
                    return 'json'
                elif filename.lower().endswith('.xml') and '<' in text_content and '>' in text_content:
                    return 'xml'
                elif filename.lower().endswith('.html') and '<html' in text_content.lower():
                    return 'html'

            except UnicodeDecodeError:
                pass

        return None
    except Exception:
        return None

def _is_likely_csv(text_content, filename):
    """更严格的CSV检测逻辑"""
    # 基本条件：必须有逗号和换行符
    if ',' not in text_content or ('\n' not in text_content and '\r' not in text_content):
        return False

    # 如果文件名不是.csv结尾，需要更严格的检测
    if not filename.lower().endswith('.csv'):
        lines = text_content.split('\n')[:5]  # 检查前5行
        valid_lines = [line.strip() for line in lines if line.strip()]

        if len(valid_lines) < 2:
            return False

        # 检查每行的逗号数量是否相似（CSV的特征）
        comma_counts = [line.count(',') for line in valid_lines]
        if not comma_counts or comma_counts[0] == 0:
            return False

        # 检查逗号数量的一致性
        avg_commas = sum(comma_counts) / len(comma_counts)
        if any(abs(count - avg_commas) > 2 for count in comma_counts):
            return False

    return True

def create_corrected_stream_info(filename, detected_type):
    """根据检测到的文件类型创建正确的StreamInfo"""
    type_mapping = {
        # 文档格式
        'csv': {'mimetype': 'text/csv', 'extension': '.csv'},
        'xls': {'mimetype': 'application/vnd.ms-excel', 'extension': '.xls'},
        'xlsx': {'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'extension': '.xlsx'},
        'docx': {'mimetype': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'extension': '.docx'},
        'pptx': {'mimetype': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'extension': '.pptx'},
        'pdf': {'mimetype': 'application/pdf', 'extension': '.pdf'},

        # 图片格式
        'png': {'mimetype': 'image/png', 'extension': '.png'},
        'jpeg': {'mimetype': 'image/jpeg', 'extension': '.jpg'},
        'gif': {'mimetype': 'image/gif', 'extension': '.gif'},
        'bmp': {'mimetype': 'image/bmp', 'extension': '.bmp'},
        'tiff': {'mimetype': 'image/tiff', 'extension': '.tiff'},
        'webp': {'mimetype': 'image/webp', 'extension': '.webp'},

        # 音频格式
        'mp3': {'mimetype': 'audio/mpeg', 'extension': '.mp3'},
        'wav': {'mimetype': 'audio/wav', 'extension': '.wav'},

        # 文本格式
        'json': {'mimetype': 'application/json', 'extension': '.json'},
        'xml': {'mimetype': 'application/xml', 'extension': '.xml'},
        'html': {'mimetype': 'text/html', 'extension': '.html'},

        # 其他格式
        'zip': {'mimetype': 'application/zip', 'extension': '.zip'},
    }

    if detected_type in type_mapping:
        mapping = type_mapping[detected_type]
        return StreamInfo(
            filename=filename,
            mimetype=mapping['mimetype'],
            extension=mapping['extension']
        )
    return None

def detect_file_type_with_system(filename):
    """使用系统file命令检测文件类型"""
    try:
        result = subprocess.run(['file', filename], capture_output=True, text=True)
        output = result.stdout.lower()

        # 图片格式检测（基于实际内容，不依赖文件名）
        if 'png image' in output:
            return 'png'
        elif 'jpeg image' in output or 'jpg image' in output:
            return 'jpeg'
        elif 'gif image' in output:
            return 'gif'
        elif 'bitmap' in output or 'bmp image' in output:
            return 'bmp'
        elif 'tiff image' in output:
            return 'tiff'

        # 文档格式检测
        elif 'pdf document' in output:
            return 'pdf'
        elif 'microsoft excel' in output or ('ole' in output and filename.lower().endswith('.xls')):
            return 'xls'
        elif 'zip archive' in output:
            if filename.lower().endswith('.xlsx'):
                return 'xlsx'
            elif filename.lower().endswith('.docx'):
                return 'docx'
            elif filename.lower().endswith('.pptx'):
                return 'pptx'
            else:
                return 'zip'

        # 文本格式检测（更严格）
        elif 'csv' in output:
            return 'csv'
        elif 'ascii text' in output and filename.lower().endswith('.csv'):
            return 'csv'

        return None
    except Exception:
        return None

def compress_image(input_path, output_path):
    img = Image.open(input_path)
    current_size = os.path.getsize(input_path)

    # 粗略的估计压缩质量，也可以从常量开始，逐步减小压缩质量，直到文件大小小于目标大小
    image_quality = int(float(target_size / current_size) * 100)
    img.save(output_path, optimize=True, quality=int(float(target_size / current_size) * 100))

    # 如果压缩后文件大小仍然大于目标大小，则继续压缩
    # 压缩质量递减，直到文件大小小于目标大小
    while os.path.getsize(output_path) > target_size:
        img = Image.open(output_path)
        image_quality -= 10
        if image_quality <= 0:
            break
        img.save(output_path, optimize=True, quality=image_quality)
    return image_quality

def convert_image_format(input_path, target_format='PNG'):
    """将图片转换为LLM支持的格式"""
    try:
        img = Image.open(input_path)

        # 如果是GIF，取第一帧
        if hasattr(img, 'is_animated') and img.is_animated:
            img = img.convert('RGB')

        # 确保是RGB模式（某些格式可能是RGBA或其他模式）
        if img.mode in ('RGBA', 'LA', 'P'):
            # 创建白色背景
            background = Image.new('RGB', img.size, (255, 255, 255))
            if img.mode == 'P':
                img = img.convert('RGBA')
            background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
            img = background
        elif img.mode != 'RGB':
            img = img.convert('RGB')

        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(suffix=f'.{target_format.lower()}', delete=False)
        temp_path = temp_file.name
        temp_file.close()

        # 保存为目标格式
        img.save(temp_path, format=target_format, quality=95 if target_format.upper() == 'JPEG' else None)

        return temp_path
    except Exception as e:
        print(f"图片格式转换失败: {e}")
        return None

def is_llm_supported_image_format(detected_type):
    """检查是否是LLM支持的图片格式"""
    # 豆包API支持的图片格式（根据错误信息推断）
    supported_formats = ['png', 'jpeg', 'jpg']
    return detected_type in supported_formats

def main():
    if len(sys.argv) != 2:
        json_return(-1, '错误的传参', {})
        sys.exit(1)
    
    filename = sys.argv[1]  # 第一个参数是文件名
    # print(f"接收到的文件名: {filename}")

    client = OpenAI(
        api_key="a784bd8d-87f0-4f9b-bcb6-e080deb6a868",
        base_url="https://ark.cn-beijing.volces.com/api/v3"
    )

    md = MarkItDown(llm_client=client, llm_model="doubao-1-5-vision-pro-32k-250115")

    # 检测文件实际类型并创建正确的StreamInfo
    detected_type = detect_file_type(filename)

    # 检测不到则用系统检测
    if not detected_type:
        detected_type = detect_file_type_with_system(filename)

    # 处理图片格式转换
    converted_file: str = None  # 类型注解
    actual_filename: str = filename

    # 检查是否是图片格式且不被LLM支持
    if detected_type in ['gif', 'bmp', 'tiff', 'webp'] or not is_llm_supported_image_format(detected_type):
        if detected_type in ['gif', 'bmp', 'tiff', 'webp', 'png', 'jpeg']:  # 确认是图片格式
            converted_file = convert_image_format(filename, 'PNG')
            if converted_file:
                actual_filename = converted_file
                # 更新检测类型为PNG
                detected_type = 'png'

    stream_info = create_corrected_stream_info(actual_filename, detected_type)

    result = None  # 初始化结果变量
    try:  # pylint: disable=broad-except
        if stream_info:
            result = md.convert(actual_filename, stream_info=stream_info)
        else:
            result = md.convert(actual_filename)

        # 成功情况下返回转换后的内容
        json_return(0, 'success', str(result.text_content))

    except Exception as e:
        # 如果转换失败，重新抛出异常让外层处理
        raise e
    finally:
        # 清理临时文件
        if converted_file and os.path.exists(converted_file):
            try:
                os.unlink(converted_file)
            except:
                pass
if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        json_return(-1, str(e), {})