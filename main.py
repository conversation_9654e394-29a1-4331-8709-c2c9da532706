# -*- coding: utf-8 -*-
from markitdown import MarkItDown, StreamInfo
from PIL import Image
from openai import OpenAI
import json
import sys
import os
import mimetypes
import subprocess
# client = AzureOpenAI(
#     api_key="69e6ad9453fb4bd2b0f387efd37d940e",  
#     api_version="2023-12-01-preview",
#     azure_endpoint="https://tw-openai-wus3-azure.zxzt123.com"
# )

# 压缩的目标图片大小，300KB
target_size = 500 * 1024

def json_return(code, message, data):
    return_data = {'code': code, 'message': message, 'data': data}
    print(json.dumps(return_data, ensure_ascii=False))

def detect_file_type(filename):
    """检测文件的实际类型，而不仅仅依赖扩展名"""
    try:
        # 读取文件开头的几个字节来判断实际格式
        with open(filename, 'rb') as f:
            header = f.read(1024)

        # 首先检查二进制文件格式（图片、Office文档等）
        # PNG文件头
        if header.startswith(b'\x89PNG\r\n\x1a\n'):
            return 'png'

        # JPEG文件头
        if header.startswith(b'\xff\xd8\xff'):
            return 'jpeg'

        # GIF文件头
        if header.startswith(b'GIF87a') or header.startswith(b'GIF89a'):
            return 'gif'

        # PDF文件头
        if header.startswith(b'%PDF'):
            return 'pdf'

        # Excel文件头
        if header.startswith(b'\xd0\xcf\x11\xe0'):  # OLE文件头 (.xls)
            return 'xls'
        elif header.startswith(b'PK\x03\x04'):  # ZIP文件头 (可能是.xlsx, .docx, .pptx等)
            # 进一步检查是否是Office文档
            if filename.lower().endswith('.xlsx'):
                return 'xlsx'
            elif filename.lower().endswith('.docx'):
                return 'docx'
            elif filename.lower().endswith('.pptx'):
                return 'pptx'
            else:
                return 'zip'

        # 检查是否是文本文件（CSV等）
        # 只有在确认不是二进制文件的情况下才检查文本格式
        try:
            # 检查是否是CSV文件（UTF-8 BOM + 文本内容）
            if header.startswith(b'\xef\xbb\xbf'):  # UTF-8 BOM
                # 去掉BOM后检查是否包含CSV特征
                content = header[3:].decode('utf-8', errors='ignore')
                if ',' in content and ('\n' in content or '\r' in content):
                    return 'csv'

            # 检查是否是纯文本CSV（没有BOM）
            content = header.decode('utf-8', errors='strict')
            # 更严格的CSV检测：必须有逗号分隔符和换行符
            if ',' in content and ('\n' in content or '\r' in content):
                # 进一步验证：检查是否看起来像CSV结构
                lines = content.split('\n')[:3]  # 检查前3行
                if len(lines) >= 2:
                    # 检查前两行是否有相似的逗号数量（CSV特征）
                    comma_counts = [line.count(',') for line in lines if line.strip()]
                    if len(comma_counts) >= 2 and comma_counts[0] > 0 and abs(comma_counts[0] - comma_counts[1]) <= 1:
                        return 'csv'
        except UnicodeDecodeError:
            # 如果不能解码为UTF-8，说明是二进制文件
            pass

        return None
    except Exception:
        return None

def create_corrected_stream_info(filename, detected_type):
    """根据检测到的文件类型创建正确的StreamInfo"""
    type_mapping = {
        # 文档格式
        'csv': {'mimetype': 'text/csv', 'extension': '.csv'},
        'xls': {'mimetype': 'application/vnd.ms-excel', 'extension': '.xls'},
        'xlsx': {'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'extension': '.xlsx'},
        'docx': {'mimetype': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'extension': '.docx'},
        'pptx': {'mimetype': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'extension': '.pptx'},
        'pdf': {'mimetype': 'application/pdf', 'extension': '.pdf'},

        # 图片格式
        'png': {'mimetype': 'image/png', 'extension': '.png'},
        'jpeg': {'mimetype': 'image/jpeg', 'extension': '.jpg'},
        'gif': {'mimetype': 'image/gif', 'extension': '.gif'},
        'bmp': {'mimetype': 'image/bmp', 'extension': '.bmp'},
        'tiff': {'mimetype': 'image/tiff', 'extension': '.tiff'},

        # 其他格式
        'zip': {'mimetype': 'application/zip', 'extension': '.zip'},
    }

    if detected_type in type_mapping:
        mapping = type_mapping[detected_type]
        return StreamInfo(
            filename=filename,
            mimetype=mapping['mimetype'],
            extension=mapping['extension']
        )
    return None

def detect_file_type_with_system(filename):
    """使用系统file命令检测文件类型"""
    try:
        result = subprocess.run(['file', filename], capture_output=True, text=True)
        output = result.stdout.lower()

        # 图片格式检测（基于实际内容，不依赖文件名）
        if 'png image' in output:
            return 'png'
        elif 'jpeg image' in output or 'jpg image' in output:
            return 'jpeg'
        elif 'gif image' in output:
            return 'gif'
        elif 'bitmap' in output or 'bmp image' in output:
            return 'bmp'
        elif 'tiff image' in output:
            return 'tiff'

        # 文档格式检测
        elif 'pdf document' in output:
            return 'pdf'
        elif 'microsoft excel' in output or ('ole' in output and filename.lower().endswith('.xls')):
            return 'xls'
        elif 'zip archive' in output:
            if filename.lower().endswith('.xlsx'):
                return 'xlsx'
            elif filename.lower().endswith('.docx'):
                return 'docx'
            elif filename.lower().endswith('.pptx'):
                return 'pptx'
            else:
                return 'zip'

        # 文本格式检测（更严格）
        elif 'csv' in output:
            return 'csv'
        elif 'ascii text' in output and filename.lower().endswith('.csv'):
            return 'csv'

        return None
    except Exception:
        return None

def compress_image(input_path, output_path):
    img = Image.open(input_path)
    current_size = os.path.getsize(input_path)

    # 粗略的估计压缩质量，也可以从常量开始，逐步减小压缩质量，直到文件大小小于目标大小
    image_quality = int(float(target_size / current_size) * 100)
    img.save(output_path, optimize=True, quality=int(float(target_size / current_size) * 100))

    # 如果压缩后文件大小仍然大于目标大小，则继续压缩
    # 压缩质量递减，直到文件大小小于目标大小
    while os.path.getsize(output_path) > target_size:
        img = Image.open(output_path)
        image_quality -= 10
        if image_quality <= 0:
            break
        img.save(output_path, optimize=True, quality=image_quality)
    return image_quality

def main():
    if len(sys.argv) != 2:
        json_return(-1, '错误的传参', {})
        sys.exit(1)
    
    filename = sys.argv[1]  # 第一个参数是文件名
    # print(f"接收到的文件名: {filename}")

    client = OpenAI(
        api_key="a784bd8d-87f0-4f9b-bcb6-e080deb6a868",
        base_url="https://ark.cn-beijing.volces.com/api/v3"
    )

    md = MarkItDown(llm_client=client, llm_model="doubao-1-5-vision-pro-32k-250115")

    # 检测文件实际类型并创建正确的StreamInfo
    detected_type = detect_file_type(filename)
    if not detected_type:
        detected_type = detect_file_type_with_system(filename)

    stream_info = create_corrected_stream_info(filename, detected_type)

    if stream_info:
        result = md.convert(filename, stream_info=stream_info)
        print(stream_info)
    else:
        print(2222)
        result = md.convert(filename)

    # with open('document.md', 'w', encoding="utf-8", errors='ignore') as f:
    #     f.write(result.text_content)
    # 成功情况下返回转换后的内容
    json_return(0, 'success', str(result.text_content))
if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        json_return(-1, str(e), {})