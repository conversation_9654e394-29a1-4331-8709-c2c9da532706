import pdfminer
import pdfminer.high_level
from typing import Union
import fitz  # PyMuPDF
from PIL import Image
import os
from io import BytesIO
import base64
import sys
import io
from typing import Binary<PERSON>, Any


from ._html_converter import HtmlConverter
from .._base_converter import DocumentConverter, DocumentConverterResult
from .._stream_info import StreamInfo
from .._exceptions import MissingDependencyException, MISSING_DEPENDENCY_MESSAGE, MarkItDownException


# Try loading optional (but in this case, required) dependencies
# Save reporting of any exceptions for later
_dependency_exc_info = None
try:
    import pdfminer
    import pdfminer.high_level
except ImportError:
    # Preserve the error and stack trace for later
    _dependency_exc_info = sys.exc_info()


ACCEPTED_MIME_TYPE_PREFIXES = [
    "application/pdf",
    "application/x-pdf",
]

ACCEPTED_FILE_EXTENSIONS = [".pdf"]


class PdfConverter(DocumentConverter):
    """
    Converts PDFs to Markdown. Most style information is ignored, so the results are essentially plain-text.
    """

    def accepts(
        self,
        file_stream: BinaryIO,
        stream_info: StreamInfo,
        **kwargs: Any,  # Options to pass to the converter
    ) -> bool:
        mimetype = (stream_info.mimetype or "").lower()
        extension = (stream_info.extension or "").lower()
        if extension in ACCEPTED_FILE_EXTENSIONS:
            return True

        for prefix in ACCEPTED_MIME_TYPE_PREFIXES:
            if mimetype.startswith(prefix):
                return True
            
        return False    
    
    def convert(
        self,
        file_stream: BinaryIO,
        stream_info: StreamInfo,
        **kwargs: Any,  # Options to pass to the converter
    ) -> DocumentConverterResult:
        # Check the dependencies
        if _dependency_exc_info is not None:
            raise MissingDependencyException(
                MISSING_DEPENDENCY_MESSAGE.format(
                    converter=type(self).__name__,
                    extension=".pdf",
                    feature="pdf",
                )
            ) from _dependency_exc_info[
                1
            ].with_traceback(  # type: ignore[union-attr]
                _dependency_exc_info[2]
            )
        local_path = stream_info.local_path
        lark_client = kwargs.get("lark_client")
        if lark_client is not None:
            pdf_info = self._pdf_info(local_path)

            if pdf_info['has_image']:
                return DocumentConverterResult(
                    markdown=self._get_lark_ocr(lark_client, local_path),
                )

        assert isinstance(file_stream, io.IOBase)  # for mypy
        return DocumentConverterResult(
            markdown=pdfminer.high_level.extract_text(file_stream),
        )
    
    def _pdf_info(self, pdf_path):

        # 打开 PDF 文件
        document = fitz.open(pdf_path)
        page_count = document.page_count
        # 遍历每一页
        for page_number in range(document.page_count):
            page = document.load_page(page_number)
            
            # 获取页面中的图片列表
            images = page.get_images(full=True)
            
            # 如果找到图片，返回 True
            if images:
                document.close()
                return {'has_image': True, 'page_count': page_count}
        
        # 如果没有找到任何图片，返回 False
        document.close()
        return {'has_image': False, 'page_count': page_count}
    
    def _compress_pdf(self, input_path, quality=50):
        filename = os.path.splitext(input_path)[0]
        doc = fitz.open(input_path)
        for page in doc:
            images = page.get_images()
            for img_info in images:
                xref = img_info[0]
                base_image = doc.extract_image(xref)
                img_bytes = base_image["image"]  # 获取图像的二进制数据
                
                # 将二进制数据转换为 Pillow 的 Image 对象
                img = Image.open(BytesIO(img_bytes))
                
                # 如果是 PNG，转为 JPEG 以压缩（可选）
                if base_image["ext"] == "png":
                    img = img.convert("RGB")
                
                # 保存为临时 JPEG 并重新嵌入
                temp_img_path = filename + ".jpg"
                img.save(temp_img_path, quality=quality, optimize=True)
                
                # 替换原图
                page.replace_image(xref, filename=temp_img_path)
                
                # 删除临时文件
                os.remove(temp_img_path)
        
        # 保存优化后的 PDF（启用深度清理和压缩）
        doc.save(filename + "_1.pdf", garbage=4, deflate=True, clean=True)
        doc.close()
        return filename + "_1.pdf"

    def _get_lark_ocr(self, lark_client, pdf_path):
        from lark_oapi.api.optical_char_recognition.v1 import BasicRecognizeImageRequest,BasicRecognizeImageRequestBody,BasicRecognizeImageResponse

        doc = fitz.open(pdf_path)
        # 生成结构化输出
        structured_content = [] 
        images_count = 0
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            elements = []

            # 提取文本块（带坐标）
            text_blocks = page.get_text("blocks")
            for block in text_blocks:
                if block[6] == 0:  # 类型为文本块
                    rect = fitz.Rect(block[:4])
                    text = block[4].strip()
                    if text:
                        elements.append({
                            "type": "text",
                            "rect": rect,
                            "content": text
                        })

            # 提取图片（新版方法）
            img_info_list = page.get_image_info(xrefs=True)  # 关键修正点
            xref_count = {}
            for img_info in img_info_list:
                xref = img_info["xref"]
                try:
                    # 获取图片位置和二进制数据
                    bbox = fitz.Rect(img_info["bbox"])  # 直接从元数据获取坐标
                    base_img = doc.extract_image(xref)
                    
                    # 处理图片重复引用
                    xref_count[xref] = xref_count.get(xref, 0) + 1
                    count = xref_count[xref]

                    elements.append({
                        "type": "image",
                        "rect": bbox,
                        "data": base_img["image"],
                        "ext": base_img["ext"],
                        "xref": f"{xref}_{count}"
                    })
                except Exception as e:
                    print(f"Page {page_num+1} 图片提取失败: {repr(e)}")

            # 按位置排序（Y轴优先）
            elements.sort(key=lambda x: (x['rect'].y0, x['rect'].x0))

            for elem in elements:
                if elem["type"] == "text":
                    structured_content.append(elem["content"])
                elif elem["type"] == "image" and images_count < 50:
                    images_count+=1
                    # 构造请求对象
                    request: BasicRecognizeImageRequest = BasicRecognizeImageRequest.builder() \
                        .request_body(BasicRecognizeImageRequestBody.builder()
                            .image(base64.b64encode(elem["data"]).decode('utf-8'))
                            .build()) \
                        .build()

                    # 发起请求
                    response: BasicRecognizeImageResponse = lark_client.optical_char_recognition.v1.image.basic_recognize(request)

                    # 处理失败返回
                    if not response.success():
                        print(f"client.optical_char_recognition.v1.image.basic_recognize failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{response.raw.content}")
                        continue
                
                    structured_content.append("\n".join(response.data.text_list))
        doc.close()
        return "\n".join(structured_content)
