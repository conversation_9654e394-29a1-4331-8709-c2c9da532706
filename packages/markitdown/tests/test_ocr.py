from openai import OpenAI
from volcengine.visual.VisualService import VisualService
import sys
from markitdown import MarkItDown

# client = AzureOpenAI(
#     api_key="69e6ad9453fb4bd2b0f387efd37d940e",  
#     api_version="2023-12-01-preview",
#     azure_endpoint="https://tw-openai-wus3-azure.zxzt123.com"
# )


def main():
   
    if len(sys.argv) < 2:
        print("没有提供文件名参数！")
        sys.exit(1)
    
    filename = sys.argv[1]  # 第一个参数是文件名
    print(f"接收到的文件名: {filename}")

    visual_service = VisualService()
    # call below method if you dont set ak and sk in $HOME/.volc/config
    visual_service.set_ak('AKLTNjdiZGMyNTAwMDY1NDQ4MjlhYzU0ZjgzZTE0YmFmODg')
    visual_service.set_sk('T1Rka1kyUmpORE5pTURNeE5EbGxNV0ZtTnpnNVpXWmlOakZqTm1RMk9XTQ==')

    md = MarkItDown(volcengine=visual_service)

    # md = MarkItDown(enable_plugins=False)
    result = md.convert(filename)

    # with open('document.md', 'w', encoding="utf-8", errors='ignore') as f:
    #     f.write(result.text_content)
    print(result.text_content)
if __name__ == "__main__":
    main()