# -*- coding: utf-8 -*-
import subprocess
import sys

def detect_file_type(filename):
    """检测文件的实际类型，而不仅仅依赖扩展名"""
    try:
        # 读取文件开头的几个字节来判断实际格式
        with open(filename, 'rb') as f:
            header = f.read(1024)
        
        # 首先检查二进制文件格式（图片、Office文档等）
        # PNG文件头
        if header.startswith(b'\x89PNG\r\n\x1a\n'):
            return 'png'
        
        # JPEG文件头
        if header.startswith(b'\xff\xd8\xff'):
            return 'jpeg'
        
        # GIF文件头
        if header.startswith(b'GIF87a') or header.startswith(b'GIF89a'):
            return 'gif'
        
        return None
    except Exception as e:
        print(f"Error: {e}")
        return None

def detect_file_type_with_system(filename):
    """使用系统file命令检测文件类型"""
    try:
        result = subprocess.run(['file', filename], capture_output=True, text=True)
        output = result.stdout.lower()
        print(f"System detection output: {output.strip()}")
        
        # 图片格式检测（基于实际内容，不依赖文件名）
        if 'png image' in output:
            return 'png'
        elif 'jpeg image' in output or 'jpg image' in output:
            return 'jpeg'
        elif 'gif image' in output:
            return 'gif'
            
        return None
    except Exception as e:
        print(f"System detection error: {e}")
        return None

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python test_detection.py <filename>")
        sys.exit(1)
    
    filename = sys.argv[1]
    print(f"Testing file: {filename}")
    
    # 测试字节检测
    detected_type1 = detect_file_type(filename)
    print(f"Byte detection result: {detected_type1}")
    
    # 测试系统检测
    detected_type2 = detect_file_type_with_system(filename)
    print(f"System detection result: {detected_type2}")
    
    # 最终结果
    final_type = detected_type1 or detected_type2
    print(f"Final detected type: {final_type}")
